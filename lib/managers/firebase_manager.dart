// ignore_for_file: prefer_interpolation_to_compose_strings

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FirebaseManager {
  static String pushToken = "";

  final _controller = StreamController<RemoteMessage?>.broadcast();
  final _controllerOnOpened = StreamController<RemoteMessage?>.broadcast();
  final _controllerOnIntialMessage =
      StreamController<RemoteMessage?>.broadcast();
  static final _controllerBg = StreamController<RemoteMessage?>.broadcast();
  static final _controllerMessageLocal =
      StreamController<RemoteMessage?>.broadcast();

  get notification => _notification;
  get onOpenedNotification => _onOpenedNotification;
  get onBgNotificaiton => _onBgNotification;
  get onInitialMessage => _onInitialMessage;
  get onLocalMessage => _onLocalMessage;

  RemoteMessage? _initialMessage;
  @pragma('vm:entry-point')
  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    log("message Background message: ${message.toMap()}");
    _controllerBg.add(message);
  }

  Future<void> _firebaseInitialMessage(RemoteMessage message) async {
    log("message Intiial message: ${message.toMap()}");
    _controllerOnIntialMessage.add(message);
  }

  Stream<RemoteMessage?> get _notification async* {
    yield null;
    yield* _controller.stream;
  }

  Stream<RemoteMessage?> get _onOpenedNotification async* {
    yield null;
    yield* _controllerOnOpened.stream;
  }

  Stream<RemoteMessage?> get _onBgNotification async* {
    yield null;
    yield* _controllerBg.stream;
  }

  Stream<RemoteMessage?> get _onInitialMessage async* {
    yield null;
    yield* _controllerOnIntialMessage.stream;
  }

  Stream<RemoteMessage?> get _onLocalMessage async* {
    yield null;
    yield* _controllerMessageLocal.stream;
  }

  Future<void> initializeApp() async {
    await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
    await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
    await FirebasePerformance.instance.setPerformanceCollectionEnabled(true);

    FirebaseMessaging.onMessage.listen(
      (RemoteMessage message) async {
        log("message: ${message.toMap()}");
        // display(message);
        _controller.add(message);
      },
    );
    FirebaseMessaging.onMessageOpenedApp.listen(
      (RemoteMessage message) async {
        log("message: ${message.toMap()}");
        // display(message);

        _controllerOnOpened.add(message);
      },
    );
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    _initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (_initialMessage != null) {
      // display(_initialMessage!);

      _firebaseInitialMessage(_initialMessage!);

      log("message _initialMessage: " + (_initialMessage?.toString() ?? ""));
      logCustomEvent(
          eventName: '_initialMessage', eventData: _initialMessage!.toMap());
    }

    initNotification();
  }

  Future addCustomKeys({required String key, required String value}) async {
    await FirebaseCrashlytics.instance.setCustomKey(key, value);
  }

  RemoteMessage? getInitialMessage() {
    return _initialMessage;
  }

  void clearInitialMessage() {
    _initialMessage = null;
  }

  Future logNonFatalException({
    required Exception exception,
    StackTrace? stackTrace,
  }) async {
    await FirebaseCrashlytics.instance.recordError(exception, stackTrace);
  }

  Future logCustomEvent({
    required String eventName,
    required Map<String, dynamic>? eventData,
  }) async {
    final FirebaseAnalytics analytics = FirebaseAnalytics.instance;

    eventData?.removeWhere((key, value) => value is! num && value is! String);

    Map<String, Object>? sanitizedData =
        eventData?.map((key, value) => MapEntry(key, value));

    await analytics.logEvent(
      name: eventName,
      parameters: sanitizedData ?? {},
    );
  }

  static Future<String> getNotification() async {
    log('getNotification');
    try {
      // Request notification permission before getting token

      NotificationSettings settings =
          await FirebaseMessaging.instance.requestPermission();

      if (settings.authorizationStatus == AuthorizationStatus.denied) {
        log("🚫 Notifications permission denied by the user.");
        return "";
      } else if (settings.authorizationStatus ==
          AuthorizationStatus.notDetermined) {
        log("⚠️ Notifications permission not determined.");
        return "";
      }
      log('getTokenFirebase NotificationSettings');

      return 'Permission Granted';
    } catch (e, s) {
      log("❌ Error in getTokenFirebase: $e\n$s");
    }
    return "";
  }

  static Future<String> getTokenFirebase() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();

      String? token = prefs.getString("TOKEN");
      pushToken = token ?? "";

      FirebaseMessaging messaging = FirebaseMessaging.instance;
      String? fcmToken = await messaging.getToken();

      if (fcmToken != null && fcmToken.isNotEmpty) {
        pushToken = fcmToken;
        await prefs.setString("TOKEN", fcmToken);
      }
      log("TOKEN: $pushToken");
      return pushToken;
    } catch (ex) {
      log("Error getting Firebase token: $ex");
      return "";
    }
  }

  static Future<void> handleNotificationMessageLocal(
    RemoteMessage data,
  ) async {
    _controllerMessageLocal.add(data);
  }

  @pragma('vm:entry-point')
  static void onBackgroundNotification(NotificationResponse response) async {
    String? payload = response.payload;
    if (payload != null) {
      final RemoteMessage message = RemoteMessage.fromMap(json.decode(payload));
      await handleNotificationMessageLocal(message);
    }
  }

  static void onForegroundNotification(NotificationResponse response) async {
    String? payload = response.payload;
    if (payload != null) {
      final RemoteMessage message = RemoteMessage.fromMap(json.decode(payload));
      await handleNotificationMessageLocal(message);
    }
  }

  static FlutterLocalNotificationsPlugin fltNotification =
      FlutterLocalNotificationsPlugin();
  static Future initNotification() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    // Request notification permissions
    await messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    // iOS-specific notification settings
    var iosInit = const DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
      requestCriticalPermission: true,
      requestProvisionalPermission: true,
    );

    var androidInit =
        const AndroidInitializationSettings('@mipmap/ic_launcher');

    var initSettings = InitializationSettings(
      android: androidInit,
      iOS: iosInit,
    );

    fltNotification.initialize(
      initSettings,
      onDidReceiveBackgroundNotificationResponse: onBackgroundNotification,
      onDidReceiveNotificationResponse: onForegroundNotification,
    );
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'high_importance_channel',
      'High Importance Notifications',
      importance: Importance.max,
    );

    await fltNotification
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  static void display(RemoteMessage message) async {
    try {
      final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      const NotificationDetails notificationDetails = NotificationDetails(
          iOS: DarwinNotificationDetails(
              sound: "default",
              presentAlert: true,
              presentBadge: true,
              presentSound: true),
          android: AndroidNotificationDetails(
            "Dunalastair",
            "Notification -- Dunalastair",
            importance: Importance.max,
            priority: Priority.high,
            styleInformation: BigTextStyleInformation(''),
          ));

      await fltNotification.show(
        id,
        message.notification!.title,
        message.notification!.body,
        notificationDetails,
        payload: json.encode(message.toMap()),
      );
    } on Exception catch (e) {
      log(e.toString());
    }
  }
}
