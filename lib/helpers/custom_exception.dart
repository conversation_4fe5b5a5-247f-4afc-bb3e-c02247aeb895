class CustomException implements Exception {
  int? codeNumber;
  String? codeString;
  String message;
  String subMessage;
  String? url;

  CustomException({
    this.codeNumber,
    this.codeString,
    this.url,
    required this.message,
    required this.subMessage,
  });

  @override
  String toString() {
    return 'Exception: $message ($subMessage) ($url) ($codeString)';
  }

  String printStack() {
    return 'Exception: $message ($subMessage)';
  }
}
