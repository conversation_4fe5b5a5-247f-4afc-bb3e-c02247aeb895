// ignore_for_file: library_private_types_in_public_api

import 'dart:developer';

import 'package:dunalastair/managers/firebase_manager.dart';
import 'package:dunalastair/presentation/loading/loading_page.dart';
import 'package:flutter/material.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const SplashScreen());
  }

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _requestData();
  }

  void _requestData() async {
    log('_requestData:: _requestData');
    String status = await FirebaseManager.getNotification();

    log('status $status');
    await Future.delayed(Duration(seconds: 3));
    Navigator.pushReplacement(context,
        MaterialPageRoute(builder: (BuildContext context) => LoadingPage()));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: double.infinity,
        width: double.infinity,
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Align(
          alignment: Alignment.center,
          child: Center(
            child: Image.asset(
              'assets/common/main_logo.png',
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height / 3.5,
            ),
          ),
        ));
  }
}
