// ignore_for_file: use_build_context_synchronously, library_private_types_in_public_api

import 'dart:developer';
import 'dart:io';

import 'package:dunalastair/managers/firebase_manager.dart';
import 'package:dunalastair/presentation/main/main_page.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart' as ph;

class LoadingPage extends StatefulWidget {
  const LoadingPage({super.key});

  @override
  _LoadingPageState createState() => _LoadingPageState();
}

class _LoadingPageState extends State<LoadingPage> {
  String token = '';

  @override
  void initState() {
    super.initState();
    _requestData();
  }

  void _requestData() async {
    log('_requestData:: _requestData');
    String token = "";
    if (Platform.isIOS) {
      var status = await ph.Permission.notification.request();
      if (status != ph.PermissionStatus.granted) {
        await ph.Permission.notification.request();
      }
    }
    try {
      try {
        token = await FirebaseManager.getTokenFirebase();
      } catch (ex) {
        log("ISSE IN GETTING TOKEN: $ex");
        token = "";
      }
    } catch (ex) {
      log("ISSE IN GETTING TOKEN IN REQUEST TRY-CATCH: $ex");
      token = "";
    }
    Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (BuildContext context) => MainPage(
                  token: token,
                )));
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            resizeToAvoidBottomInset: false,
            backgroundColor: Colors.white,
            body: _body()));
  }

  Widget _body() {
    return const Center(
        child: SizedBox(
      width: 60,
      height: 60,
      child: CircularProgressIndicator(
        color: Colors.black,
      ),
    ));
  }
}
