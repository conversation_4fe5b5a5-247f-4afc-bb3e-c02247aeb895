// // ignore_for_file: prefer_interpolation_to_compose_strings, library_private_types_in_public_api, unused_element, avoid_print, deprecated_member_use, prefer_collection_literals, body_might_complete_normally_nullable, prefer_adjacent_string_concatenation, unnecessary_brace_in_string_interps

// import 'dart:async';
// import 'dart:convert';
// import 'dart:io';

// import 'package:dunalastair/helpers/custom_exception.dart';
// import 'package:dunalastair/managers/firebase_manager.dart';
// import 'package:firebase_analytics/firebase_analytics.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
// import 'package:get_it/get_it.dart';
// import 'package:url_launcher/url_launcher.dart';

// class MainPage extends StatefulWidget {
//   final String token;

//   const MainPage({super.key, required this.token});

//   @override
//   _MainPageState createState() => _MainPageState();
// }

// class _MainPageState extends State<MainPage> {
//   var defaultUrl = "https://login.dunalastair.cl/default.aspx";
//   late StreamSubscription<RemoteMessage?> _notificationSubscription;
//   late StreamSubscription<RemoteMessage?> _onOpenedNotificationSubscription;
//   final CookieManager cookieManager2 = CookieManager.instance();
//   final FirebaseManager _firebaseManager =
//       GetIt.instance.get<FirebaseManager>();

//   InAppWebViewController? wController;

//   String dataSent = '';
//   String cookies = '';

//   bool urlVisible = false;

//   String token = "";
//   String _url = "";

//   bool isLoading = false;
//   Future checkFcmToken() async {
//     setState(() {
//       isLoading = true;
//     });
//     if (widget.token.isEmpty) {
//       token = await FirebaseManager.getTokenFirebase();

//       if (token.isEmpty) {
//         checkFcmToken();
//         return;
//       }
//     } else {
//       token = widget.token;
//     }

//     _loadHTML();
//     _notificationSubscription =
//         _firebaseManager.notification.listen((notification) async {
//       if (null != notification) {
//         showLocalNotification(notification);
//       }
//     });

//     _onOpenedNotificationSubscription =
//         _firebaseManager.onOpenedNotification.listen((notification) async {
//       if (null != notification) {
//         showOnOpenedLocalNotification(notification);
//       }
//     });
//     setState(() {
//       isLoading = false;
//     });
//   }

//   @override
//   void initState() {
//     super.initState();

//     checkFcmToken();
//   }

//   @override
//   void dispose() {
//     _notificationSubscription.cancel();
//     _onOpenedNotificationSubscription.cancel();
//     super.dispose();
//   }

//   void _deleteCookies() async {
//     final url =
//         WebUri(defaultUrl); //loginunificado-test.dunalastair.cl/login.aspx");
//     await cookieManager2.deleteCookies(url: url);
//     await cookieManager2.deleteAllCookies();

//     _firebaseManager.logCustomEvent(eventName: "deleteCookies", eventData: {});
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//         child: Scaffold(
//       resizeToAvoidBottomInset: false,
//       backgroundColor: Colors.white,
//       body: isLoading
//           ? const Center(
//               child: SizedBox(
//                   height: 50,
//                   width: 50,
//                   child: CircularProgressIndicator(
//                     color: Colors.black,
//                   )))
//           : RefreshIndicator(
//               onRefresh: () async {
//                 await _webViewController.reload();
//               },
//               child: Column(children: [
//                 Expanded(
//                     child: Stack(children: [
//                   _body(),
//                 ])),
//                 urlVisible ? Container() : Center(child: Text(_url))
//               ]),
//             ),
//     ));
//   }

//   void _showUrl() async {
//     _url = (await wController?.getUrl()).toString();

//     setState(() {
//       urlVisible = !urlVisible;
//     });
//   }

//   void _logout() {
//     _deleteCookies();
//   }

//   final _webViewKey = GlobalKey();

//   late final InAppWebViewController _webViewController;
//   Widget _body() {
//     RemoteMessage? message = _firebaseManager.checkInitialMessage();

//     String? url = null != message ? _urlFromMessage(message) : null;

//     _firebaseManager.cleanInitialMessage();

//     return InAppWebView(
//       iosShouldAllowDeprecatedTLS: (controller, challenge) async {
//         return IOSShouldAllowDeprecatedTLSAction.ALLOW;
//       },
//       onJsAlert: (controller, message) async {
//         await FirebaseAnalytics.instance.logEvent(
//           name: "form_submission_result",
//           parameters: {
//             "message": message.message.toString(),
//             "url": message.url.toString(),
//           },
//         );
//       },
//       initialOptions: InAppWebViewGroupOptions(
//         android: AndroidInAppWebViewOptions(
//             allowContentAccess: true,
//             allowFileAccess: true,
//             supportMultipleWindows: true,
//             thirdPartyCookiesEnabled: true),
//         crossPlatform: InAppWebViewOptions(
//           supportZoom: true,
//           verticalScrollBarEnabled: true,
//           horizontalScrollBarEnabled: true,
//           cacheEnabled: true,
//           javaScriptCanOpenWindowsAutomatically: true,
//           // useShouldOverrideUrlLoading: true,
//           mediaPlaybackRequiresUserGesture: false,
//           allowFileAccessFromFileURLs: true,
//           allowUniversalAccessFromFileURLs: true,
//           userAgent: Platform.isIOS
//               ? 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_1_2 like Mac OS X) AppleWebKit/605.1.15' +
//                   ' (KHTML, like Gecko) Version/13.0.1 Mobile/15E148 Safari/604.1'
//               : 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) ' +
//                   'AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Mobile Safari/537.36',
//           applicationNameForUserAgent: 'Dunalastair',
//           preferredContentMode: UserPreferredContentMode.RECOMMENDED,
//         ), // here you change the mode
//       ),
//       iosOnWebContentProcessDidTerminate: (controller) {},
//       iosOnNavigationResponse: (controller, navigationResponse) async {
//         return IOSNavigationResponseAction.ALLOW;
//       },
//       iosOnDidReceiveServerRedirectForProvisionalNavigation: (controller) {},
//       key: _webViewKey,
//       onWebViewCreated: (controller) {
//         _webViewController = controller;
//       },
//       initialData: InAppWebViewInitialData(
//         data: _loadHTML(),
//         baseUrl: WebUri(defaultUrl),
//       ),
//       initialUrlRequest: url != null
//           ? url.contains('dunout=1')
//               ? URLRequest(
//                   url: WebUri(
//                       'https://docs.google.com/gview?embedded=true&url=$url'))
//               : URLRequest(url: WebUri(url))
//           : URLRequest(url: WebUri(defaultUrl)),
//       onReceivedError: (controller, request, error) {
//         _logCustomError(
//             method: "onReceivedError",
//             message: error.description,
//             codeStr: '-1',
//             url: request.url.toString());

//         _firebaseManager.logCustomEvent(
//             eventName: "onReceivedError", eventData: error.toMap());
//       },
//       onReceivedHttpError: (controller, request, errorResponse) {
//         _logCustomError(
//             method: "onReceivedHttpError",
//             message: errorResponse.reasonPhrase ?? '',
//             codeStr: (errorResponse.statusCode ?? -1).toString(),
//             url: request.url.toString());

//         _firebaseManager.logCustomEvent(
//             eventName: "onReceivedHttpError", eventData: errorResponse.toMap());
//       },
//       onCreateWindow: (controller, createWindowAction) async {
//         var uri = createWindowAction.request.url;
//         if (uri == null) {
//           return true;
//         }
//         if (uri.toString().contains('attachment')) {
//           await _openLinkExternalBrowser(uri);
//           return false;
//         } else {
//           return true;
//         }
//       },
//       shouldOverrideUrlLoading: (controller, navigationAction) async {
//         Uri uri = navigationAction.request.url!;

//         Map queryParameters = uri.queryParameters;

//         bool dunout = (queryParameters['dunout'] ?? '0') == '1' ? true : false;
//         if (dunout) {
//           await _openLinkExternalBrowser(uri);
//           return NavigationActionPolicy.CANCEL;
//         } else {
//           return NavigationActionPolicy.ALLOW;
//         }
//       },
//       onLoadStop: (controller, url) async {
//         wController = controller;

//         wController?.addJavaScriptHandler(
//             handlerName: 'Alert', callback: (args) {});

//         if (url.toString().contains('logout')) {
//           _logout();
//         }
//       },
//       onLoadError: (controller, url, code, message) {
//         _logCustomError(
//             method: "onLoadError",
//             message: message,
//             codeStr: code.toString(),
//             url: url?.toString() ?? '');

//         _firebaseManager.logCustomEvent(eventName: "onLoadError", eventData: {
//           'url': url.toString(),
//           'code': code,
//           'message': message
//         });
//       },
//       // onDownloadStart: (controller, url) async {
//       //   print("onDownloadStart $url");

//       //   Directory? appDir = Platform.isAndroid
//       //       ? await getTemporaryDirectory()
//       //       : await getApplicationDocumentsDirectory();

//       //   String newPath = "";
//       //   List<String> paths = appDir.path.split("/");
//       //   for (int x = 1; x < paths.length; x++) {
//       //     String folder = paths[x];

//       //     log("Folder is: $folder");
//       //     if (folder != "Android") {
//       //       newPath += "/" + folder;
//       //     } else {
//       //       break;
//       //     }
//       //   }
//       //   // newPath = newPath + "/MWSApp";
//       //   appDir = Directory(newPath);
//       //   if (await appDir.exists()) {
//       //     log("Exsists");
//       //   }
//       //   String tempPath = appDir.path;
//       //   final taskId = await FlutterDownloader.enqueue(
//       //     url: url.path,
//       //     savedDir: tempPath,
//       //     showNotification:
//       //         true, // show download progress in status bar (for Android)
//       //     openFileFromNotification:
//       //         true, // click on notification to open downloaded file (for Android)
//       //   );
//       // },
//     );
//   }

//   void _logCustomError(
//       {required String method,
//       required String message,
//       required String codeStr,
//       required String? url}) async {
//     CustomException exception = CustomException(
//         message: method, subMessage: message, codeString: codeStr, url: url);

//     await _firebaseManager.logNonFatalException(exception: exception);

//     if (null != url) {
//       await _firebaseManager.addCustomKeys(key: 'url', value: url);
//     }
//     await _firebaseManager.addCustomKeys(key: 'method', value: method);
//     await _firebaseManager.addCustomKeys(key: 'code', value: codeStr);
//   }

//   String _loadHTML() {
//     FirebaseAnalytics.instance.logEvent(
//       name: "form_submission",
//       parameters: {
//         "token": token.toString(),
//         "url": defaultUrl.toString(),
//       },
//     );

//     return '''
//      <html>
//       <body onload="submitForm()">
//         <form id="f" name="f" method="post" action="${defaultUrl}">
//           <input type="hidden" name="token" id="token" value="${token}" />
//         </form>
//         <script>

//          function handleResponse() {
//         var form = document.getElementById('f');
//         var xhr = new XMLHttpRequest();
//         xhr.onreadystatechange = function() {
//           if (xhr.readyState === XMLHttpRequest.DONE) {
           
//           }
//         };
//         xhr.open(form.method, form.action, true);
//         xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
//         xhr.send(new FormData(form));
//       }
//           function submitForm() {
//             var tokenValue = document.getElementById('token').value;
//             if (tokenValue) {
       
//               document.getElementById('f').submit();
//               handleResponse();
//             } else {
//             }
//           }
//         </script>
//       </body>
//     </html>
//   ''';
//   }

//   void _loadUrlFromNotification(String url) {
//     wController?.loadUrl(urlRequest: URLRequest(url: WebUri(url)));
//   }

//   void showOnOpenedLocalNotification(RemoteMessage message) async {
//     String? url = _urlFromMessage(message);

//     if (null == url) {
//       return;
//     }

//     Uri uri = Uri.dataFromString(url);

//     Map queryParameters = uri.queryParameters;

//     bool dunout = (queryParameters['dunout'] ?? '0') == '1' ? true : false;

//     if (dunout) {
//       _openLinkExternalBrowser(uri);
//     } else {
//       _loadUrlFromNotification(url);
//     }
//   }

//   String? _urlFromMessage(RemoteMessage message) {
//     String? url;
//     Map body = Map();
//     if (message.data['body'] is String) {
//       body = json.decode(message.data['body']);
//     } else {
//       body = message.data['body'] ?? Map();
//     }
//     url = body['url'];

//     return url;
//   }

//   void showLocalNotification(RemoteMessage message) async {
//     if (true == message.notification?.title?.isNotEmpty) {
//       showDialog(
//         context: context,
//         builder: (BuildContext context) {
//           // return object of type Dialog
//           return AlertDialog(
//             title: Text(message.notification?.title ?? ''),
//             content: Text(message.notification?.body ?? ''),
//             actions: <Widget>[
//               // usually buttons at the bottom of the dialog

//               InkWell(
//                   onTap: () {
//                     Navigator.pop(context);
//                   },
//                   child: const Text("Aceptar")),
//             ],
//           );
//         },
//       );
//     }

//     String? url = _urlFromMessage(message);

//     if (null == url) {
//       return;
//     }

//     Uri uri = Uri.dataFromString(url);

//     Map queryParameters = uri.queryParameters;

//     bool dunout = (queryParameters['dunout'] ?? '0') == '1' ? true : false;

//     if (dunout) {
//       Uri uri = Uri.parse(url);
//       _openLinkExternalBrowser(uri);
//     } else {
//       _loadUrlFromNotification(url);
//     }
//   }

//   Future _openLinkExternalBrowser(Uri uri) async {
//     if (await canLaunchUrl(uri)) {
//       await launchUrl(uri, mode: LaunchMode.externalApplication);
//     }
//   }
// }
