// ignore_for_file: prefer_interpolation_to_compose_strings, library_private_types_in_public_api, unused_element, avoid_print, deprecated_member_use, prefer_collection_literals, body_might_complete_normally_nullable, prefer_adjacent_string_concatenation, unnecessary_brace_in_string_interps, unused_field, use_build_context_synchronously, unnecessary_null_comparison

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dunalastair/helpers/custom_exception.dart';
import 'package:dunalastair/managers/firebase_manager.dart';
import 'package:dunalastair/presentation/main/pdf_view.dart';
// import 'package:dunalastair/presentation/main/blog_page.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get_it/get_it.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

// import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
// import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

class MainPage extends StatefulWidget {
  final String token;

  const MainPage({super.key, required this.token});

  @override
  _MainPageState createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> with WidgetsBindingObserver {
  var defaultUrl = "https://login.dunalastair.cl/default.aspx";
  var previousURL = "https://login.dunalastair.cl/default.aspx";

  var homeDefaultURL = "https://login.dunalastair.cl/default.aspx";
  List<String> arrayStack = [];

  Future<void> downloadAndOpenPDF(Uri url1) async {
    final url = url1.toString();

    if (url != "null" && url.isNotEmpty) {
      try {
        // Request storage permission if on Android
        if (Platform.isAndroid) {
          final status = await Permission.storage.request();
          if (!status.isGranted) {
            print("Storage permission denied");
            return;
          }
        }

        // Extract the file name from the URL
        final fileName = url.split('/').last;
        Directory? directory = Platform.isAndroid
            ? await getTemporaryDirectory()
            : await getApplicationDocumentsDirectory();
        final filePath = '${directory.path}/$fileName.pdf';

        // Use Dio to download the file
        final dio = Dio();
        await dio.download(url, filePath, onReceiveProgress: (received, total) {
          if (total != -1) {
            print(
                'Downloading: ${(received / total * 100).toStringAsFixed(0)}%');
          }
        });

        // Notify the user of success
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("File downloaded successfully: $fileName"),
        ));

        print("File saved to $filePath");
        await OpenFilex.open(filePath);
      } catch (e) {
        print("Error during download: $e");
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("Failed to download file ${e.toString()}"),
        ));
      }
    } else {
      print("Invalid download URL");
    }
    // try {
    //   final response = await http.get(url);
    //   final bytes = response.bodyBytes;

    //   final dir = await getTemporaryDirectory();
    //   final file = File('${dir.path}/document.pdf');
    //   await file.writeAsBytes(bytes);

    //   final result = await OpenFilex.open(file.path);
    //   print('OpenFile result: ${result.message}');
    // } catch (e) {
    //   print("Failed to download/open PDF: $e");
    // }
  }

  Future<void> loadURLFunction(String url) async {
    try {
      if (Platform.isAndroid) {
        if (url.contains('.pdf') ||
            url.contains('.jpg') ||
            url.contains('.png') ||
            url.contains('.doc') ||
            url.contains('.xsls')) {
          _openLinkExternalBrowser(Uri.parse(url));
          loadURLFunction(previousURL);
          return;
        }
      }
      // if (url.toLowerCase().contains(".pdf") ||
      //     url.contains("attachment.aspx")) {
      //   await downloadAndOpenPDF(Uri.parse(url));

      // Navigator.push(
      //     context,
      //     MaterialPageRoute(
      //         builder: (context) => PDFViewerScreen(pdfUrl: url)));
      // return;
      // }

      // Otherwise, open URL in WebView
      await _webViewController!.loadUrl(
        urlRequest: URLRequest(url: WebUri(url)),
      );
    } catch (e) {
      print("Error handling URL: $e");
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text("Error handling URL: $e"),
      ));

      // Optionally reload previous URL if available
      if (previousURL.isNotEmpty && previousURL != url) {
        await loadURLFunction(previousURL);
      }
    }
  }

  String recentLoadURL = "";
  bool isLoadingURL = false;
  pushToStack(String url) async {
    if (recentLoadURL != url) {
      log("pushToStack URL :: $url, Before arrayStack now ${arrayStack.length}:: ${arrayStack} ");
      if (url == defaultUrl) {
        return;
      }

      if (arrayStack.isNotEmpty) {
        if (homeDefaultURL == url) {
          return;
        }
      }

      arrayStack.add(url);

      log("pushToStack URL :: $url, After arrayStack now ${arrayStack.length}:: ${arrayStack} ");
      setState(() {});
    } else {
      recentLoadURL = "";
    }
  }

  popFromStack() async {
    if (arrayStack.length < 2) {
      log("Cannot pop: The stack has less than two URLs.");
      return;
    }
    log("popFromStack arrayStack Before ${arrayStack.length}:: ${arrayStack}");

    // Get the second last URL
    String url = arrayStack[arrayStack.length - 2];
    recentLoadURL = url;
    log("popFromStack URL:: $url, arrayStack After ${arrayStack.length}:: ${arrayStack}");

    // Update loading state and load the URL
    setState(() {
      isLoadingURL = true;
    });

    try {
      loadURLFunction(url);
    } catch (e) {
      log("Error loading URL: $e");
    }

    setState(() {
      isLoadingURL = false;
    });
    // Remove the last URL from the stack
    arrayStack.removeLast();
    setState(() {});
  }

  late StreamSubscription<RemoteMessage?> _notificationSubscription;
  late StreamSubscription<RemoteMessage?> _onOpenedNotificationSubscription;
  late StreamSubscription<RemoteMessage?> _onBgNotification;
  late StreamSubscription<RemoteMessage?> _onInitialMessage;
  late StreamSubscription<RemoteMessage?> _onLocalMessage;

  final CookieManager cookieManager2 = CookieManager.instance();
  final FirebaseManager _firebaseManager =
      GetIt.instance.get<FirebaseManager>();
  showSnackBar(msg) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text(msg),
      duration: const Duration(milliseconds: 300),
    ));
  }

  String dataSent = '';
  String cookies = '';
  bool urlVisible = false;
  String token = "";
  bool isLoading = false;

  int tries = 0;
  Future checkFcmToken() async {
    setState(() {
      isLoading = true;
    });
    if (widget.token.isEmpty) {
      token = await FirebaseManager.getTokenFirebase();

      if (token.isEmpty) {
        tries++;
        if (tries < 3) {
          await checkFcmToken();

          return;
        }
      }
    } else {
      token = widget.token;
    }

    _loadHTML();
    _notificationSubscription =
        _firebaseManager.notification.listen((notification) async {
      if (null != notification) {
        showNotificationInApp(notification);
      }
    });

    _onOpenedNotificationSubscription =
        _firebaseManager.onOpenedNotification.listen((notification) async {
      if (null != notification) {
        showNotificationInApp(notification);
      }
    });

    _onBgNotification =
        _firebaseManager.onBgNotificaiton.listen((notification) async {
      if (null != notification) {
        showNotificationInApp(notification);
      }
    });
    _onInitialMessage =
        _firebaseManager.onInitialMessage.listen((notification) async {
      if (null != notification) {
        showNotificationInApp(notification);
      }
    });

    _onLocalMessage =
        _firebaseManager.onLocalMessage.listen((RemoteMessage? notification) {
      if (null != notification) {
        performNotificationTapDirectly(notification);
      }
    });

    setState(() {
      isLoading = false;
    });
  }

  navigateScreens() async {
    await checkFcmToken();

    Future.delayed(Duration(seconds: 4)).whenComplete(() {
      fetchInitialMessageAndPerformRedirection();
    });
  }

  fetchInitialMessageAndPerformRedirection() {
    var message = _firebaseManager.getInitialMessage();
    if (null != message) {
      Future.delayed(Duration(microseconds: 50)).whenComplete(() async {
        _firebaseManager.clearInitialMessage();
        performNotificationTapDirectly(message);
      });
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    fetchInitialMessageAndPerformRedirection();
    super.didChangeAppLifecycleState(state);
  }

  @override
  void initState() {
    super.initState();

    navigateScreens();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    _notificationSubscription.cancel();
    _onOpenedNotificationSubscription.cancel();
    _onBgNotification.cancel();
    _onInitialMessage.cancel();
    _onLocalMessage.cancel();
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  void _deleteCookies() async {
    final url =
        WebUri(defaultUrl); //loginunificado-test.dunalastair.cl/login.aspx");
    await cookieManager2.deleteCookies(url: url);
    await cookieManager2.deleteAllCookies();

    _firebaseManager.logCustomEvent(eventName: "deleteCookies", eventData: {});
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.white,
        body: isLoading
            ? const Center(
                child: SizedBox(
                  height: 50,
                  width: 50,
                  child: CircularProgressIndicator(
                    color: Colors.black,
                  ),
                ),
              )
            : RefreshIndicator(
                onRefresh: () async {
                  await _webViewController!.reload();
                },
                child: _body(),
              ),
      ),
    );
  }

  void _logout() {
    _deleteCookies();
  }

  final _webViewKey = GlobalKey();

  InAppWebViewController? _webViewController;
  Widget _body() {
    String? url;

    return WillPopScope(
      onWillPop: () async {
        if (arrayStack.length > 1 && !isLoadingURL) {
          popFromStack();
          return false;
        }
        return false;
      },
      child: Column(
        children: [
          (arrayStack.length > 1 && !isLoadingURL)
              ? Container(
                  height: 40,
                  width: MediaQuery.of(context).size.width,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        child: const Row(
                          children: [
                            Icon(
                              Icons.arrow_back_ios_new_rounded,
                              size: 20,
                              color: Color(0xff003595),
                            ),
                            SizedBox(
                              width: 5,
                            ),
                            Text(
                              "Volver",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xff003595),
                              ),
                            )
                          ],
                        ),
                        onTap: () {
                          popFromStack();
                        },
                      ),
                      // Image.asset(
                      //   'assets/common/main_logo.png',
                      //   // width: MediaQuery.of(context).size.width,
                      //   height: 30,
                      // ),

                      Container()
                      // IconButton(
                      //   icon: const Icon(
                      //     Icons.logout_rounded,
                      //     size: 30,
                      //     weight: 10,
                      //     color: Color(0xff003595),
                      //   ),
                      //   onPressed: () {
                      //     if (isPop) {
                      //       _logout();
                      //     }
                      //   },
                      // ),
                    ],
                  ),
                )
              : Container(),
          SizedBox(
            height: MediaQuery.of(context).size.height -
                (arrayStack.length > 1 && !isLoadingURL ? 75 : 10),
            width: MediaQuery.of(context).size.width,
            child: InAppWebView(
              iosShouldAllowDeprecatedTLS: (controller, challenge) async {
                return IOSShouldAllowDeprecatedTLSAction.ALLOW;
              },
              onJsAlert: (controller, message) async {
                await FirebaseAnalytics.instance.logEvent(
                  name: "form_submission_result",
                  parameters: {
                    "message": message.message.toString(),
                    "url": message.url.toString(),
                  },
                );
              },
              initialOptions: InAppWebViewGroupOptions(
                android: AndroidInAppWebViewOptions(
                    allowContentAccess: true,
                    allowFileAccess: true,
                    supportMultipleWindows: true,
                    thirdPartyCookiesEnabled: true),
                ios: IOSInAppWebViewOptions(),
                crossPlatform: InAppWebViewOptions(
                  supportZoom: true, javaScriptEnabled: true,
                  verticalScrollBarEnabled: true,
                  horizontalScrollBarEnabled: true,
                  cacheEnabled: true,
                  javaScriptCanOpenWindowsAutomatically: true,
                  // useShouldOverrideUrlLoading: true,
                  mediaPlaybackRequiresUserGesture: false,
                  allowFileAccessFromFileURLs: true,
                  allowUniversalAccessFromFileURLs: true,
                  userAgent: Platform.isIOS
                      ? 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_1_2 like Mac OS X) AppleWebKit/605.1.15' +
                          ' (KHTML, like Gecko) Version/13.0.1 Mobile/15E148 Safari/604.1'
                      : 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) ' +
                          'AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Mobile Safari/537.36',
                  applicationNameForUserAgent: 'Dunalastair',
                  preferredContentMode: UserPreferredContentMode.RECOMMENDED,
                ), // here you change the mode
              ),
              iosOnWebContentProcessDidTerminate: (controller) {},
              iosOnNavigationResponse: (controller, navigationResponse) async {
                return IOSNavigationResponseAction.ALLOW;
              },
              iosOnDidReceiveServerRedirectForProvisionalNavigation:
                  (controller) {},
              key: _webViewKey,
              onWebViewCreated: (controller) {
                _webViewController = controller;

                // Inject JavaScript to capture click events on anchor tags
                controller.addJavaScriptHandler(
                  handlerName: "linkClickHandler",
                  callback: (args) async {
                    // String targetValue = args[0];
                    // String anchorText = args[1];
                    String uri = args[2];

                    if (uri.toString().contains('mailto') ||
                        uri.toString().contains('.ics')) {
                      setState(() {
                        isLoading = true;
                      });
                      await _openLinkExternalBrowser(Uri.parse(uri));
                      setState(() {
                        isLoading = false;
                      });
                      loadURLFunction(previousURL);
                      return true;
                    } else {
                      await loadURLFunction(uri);
                      return false;
                    }

                    // You can process the values further here
                  },
                );

                // JavaScript to capture clicks and send the data back to Flutter
                controller.evaluateJavascript(source: """
                    document.addEventListener('click', function(event) {
                      let element = event.target;
            
                      // Traverse up the DOM to find the nearest anchor tag
                      while (element && element.tagName !== 'A') {
                        element = element.parentElement;
                      }
            
                      if (element && element.tagName === 'A') {
                        const targetValue = element.getAttribute('target') || '_self';
                        const anchorText = element.innerText || '';
                        const href = element.href || '';
            
                        // Send data back to Flutter
                        window.flutter_inappwebview.callHandler('linkClickHandler', targetValue, anchorText, href);
                        event.preventDefault(); // Prevent the default link click behavior
                      }
                    });
                  """);
              },
              initialData: InAppWebViewInitialData(
                data: _loadHTML(),
                baseUrl: WebUri(defaultUrl),
              ),
              initialUrlRequest: url != null
                  //     ? url.contains('dunout=1')
                  //         ? URLRequest(
                  //             url: WebUri(
                  //                 'https://docs.google.com/gview?embedded=true&url=$url'))
                  //         :

                  ? URLRequest(url: WebUri(url))
                  : URLRequest(url: WebUri(defaultUrl)),
              onReceivedError: (controller, request, error) async {
                if (request.url.toString().contains('mailto') ||
                    request.url.toString().contains('.ics')) {
                  setState(() {
                    isLoading = true;
                  });
                  await _openLinkExternalBrowser(request.url);
                  setState(() {
                    isLoading = false;
                  });
                  loadURLFunction(previousURL);
                }
                _logCustomError(
                    method: "onReceivedError",
                    message: error.description,
                    codeStr: '-1',
                    url: request.url.toString());

                _firebaseManager.logCustomEvent(
                    eventName: "onReceivedError", eventData: error.toMap());
              },
              onReceivedHttpError: (controller, request, errorResponse) {
                _logCustomError(
                    method: "onReceivedHttpError",
                    message: errorResponse.reasonPhrase ?? '',
                    codeStr: (errorResponse.statusCode ?? -1).toString(),
                    url: request.url.toString());

                _firebaseManager.logCustomEvent(
                    eventName: "onReceivedHttpError",
                    eventData: errorResponse.toMap());
              },
              onCreateWindow: (controller, createWindowAction) async {
                WebUri? uri = createWindowAction.request.url;

                if (uri == null) {
                  return true;
                }

                if (uri.toString().contains('mailto') ||
                    uri.toString().contains('.ics')) {
                  setState(() {
                    isLoading = true;
                  });
                  await _openLinkExternalBrowser(uri);
                  setState(() {
                    isLoading = false;
                  });

                  loadURLFunction(previousURL);

                  return true;
                } else {
                  await loadURLFunction(uri.toString());
                  return false;
                }
              },
              shouldOverrideUrlLoading: (controller, navigationAction) async {
                Uri uri = navigationAction.request.url!;

                if (uri.toString().contains('mailto') ||
                    uri.toString().contains('.ics')) {
                  setState(() {
                    isLoading = true;
                  });
                  await _openLinkExternalBrowser(uri);
                  setState(() {
                    isLoading = false;
                  });
                  loadURLFunction(previousURL);

                  return NavigationActionPolicy.ALLOW;
                } else {
                  return NavigationActionPolicy.ALLOW;
                }
              },
              onLoadStop: (controller, url) async {
                _webViewController = controller;

                _webViewController!.addJavaScriptHandler(
                    handlerName: 'Alert', callback: (args) {});

                if (url.toString().contains('logout')) {
                  _logout();
                }
                if (!(url.toString().contains('mailto') ||
                    url.toString().contains('.ics'))) {
                  previousURL = url.toString();
                }
              },
              onLoadStart: (ctrl, url) {
                if (!(url.toString().contains('mailto') ||
                    url.toString().contains('.ics'))) {
                  previousURL = url.toString();
                  pushToStack(previousURL);
                }
              },
              onLoadError: (controller, uri, code, message) {
                _logCustomError(
                    method: "onLoadError",
                    message: message,
                    codeStr: code.toString(),
                    url: url.toString());

                _firebaseManager.logCustomEvent(
                    eventName: "onLoadError",
                    eventData: {
                      'url': url.toString(),
                      'code': code,
                      'message': message
                    });
              },
              onDownloadStart: (controller, url) {
                downloadAndOpenPDF(url);
              },
              onDownloadStartRequest: (controller, downloadStartRequest) async {
                final url = downloadStartRequest.url.toString();
                log("onDownloadStartRequest:: URL:: $url");
                if (url != "null" && url.isNotEmpty) {
                  try {
                    // Request storage permission if on Android
                    if (Platform.isAndroid) {
                      final status = await Permission.storage.request();
                      if (!status.isGranted) {
                        print("Storage permission denied");
                        return;
                      }
                    }

                    // Extract the file name from the URL
                    final fileName = url.split('/').last;
                    Directory? directory = Platform.isAndroid
                        ? await getTemporaryDirectory()
                        : await getApplicationDocumentsDirectory();
                    final filePath = '${directory.path}/$fileName';

                    // Use Dio to download the file
                    final dio = Dio();
                    await dio.download(url, filePath,
                        onReceiveProgress: (received, total) {
                      if (total != -1) {
                        print(
                            'Downloading: ${(received / total * 100).toStringAsFixed(0)}%');
                      }
                    });

                    // Notify the user of success
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                      content: Text("File downloaded successfully: $fileName"),
                    ));

                    print("File saved to $filePath");
                  } catch (e) {
                    print("Error during download: $e");
                    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                      content: Text("Failed to download file"),
                    ));
                  }
                } else {
                  print("Invalid download URL");
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  void _logCustomError(
      {required String method,
      required String message,
      required String codeStr,
      required String? url}) async {
    CustomException exception = CustomException(
        message: method, subMessage: message, codeString: codeStr, url: url);

    await _firebaseManager.logNonFatalException(exception: exception);

    if (null != url) {
      await _firebaseManager.addCustomKeys(key: 'url', value: url);
    }
    await _firebaseManager.addCustomKeys(key: 'method', value: method);
    await _firebaseManager.addCustomKeys(key: 'code', value: codeStr);
  }

  String _loadHTML() {
    FirebaseAnalytics.instance.logEvent(
      name: "form_submission",
      parameters: {
        "token": token.toString(),
        "url": defaultUrl.toString(),
      },
    );

    return '''
     <html>
      <body onload="submitForm()">
        <form id="f" name="f" method="post" action="${defaultUrl}">
          <input type="hidden" name="token" id="token" value="${token}" />
        </form>
        <script>

         function handleResponse() {
        var form = document.getElementById('f');
        var xhr = new XMLHttpRequest();
        xhr.onreadystatechange = function() {
          if (xhr.readyState === XMLHttpRequest.DONE) {
           
          }
        };
        xhr.open(form.method, form.action, true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.send(new FormData(form));
      }
          function submitForm() {
            var tokenValue = document.getElementById('token').value;
            if (tokenValue) {
       
              document.getElementById('f').submit();
              handleResponse();
            } else {
            }
          }
        </script>
      </body>
    </html>
  ''';
  }

  void performNotificationTapDirectly(RemoteMessage message) async {
    String? url = _urlFromMessage(message);

    if (null == url) {
      return;
    }

    Uri uri = Uri.dataFromString(url);
    if (uri.toString().contains('mailto') || uri.toString().contains('.ics')) {
      setState(() {
        isLoading = true;
      });
      await _openLinkExternalBrowser(uri);
      setState(() {
        isLoading = false;
      });
      loadURLFunction(previousURL);
    } else {
      loadURLFunction(url);
    }
  }

  String? _urlFromMessage(RemoteMessage message) {
    String? url;

    url = message.data['data[url]'] ?? message.data['url'];

    if (url == null && message.data['body'] != null) {
      try {
        Map body = message.data['body'] is String
            ? json.decode(message.data['body'])
            : message.data['body'];
        url = body['url'];
      } catch (e) {
        print("Error parsing message body: $e");
      }
    }
    if (url == null && message.data['notification'] != null) {
      // Try to parse body if URL wasn't found
      try {
        Map body = message.data['notification'] is String
            ? json.decode(message.data['notification'])
            : message.data['notification'];
        url = body['url'];
      } catch (e) {
        print("Error parsing message body: $e");
      }
    }

    if (url == null && message.data['data'] != null) {
      // Try to parse body if URL wasn't found
      try {
        Map body = message.data['data'] is String
            ? json.decode(message.data['data'])
            : message.data['data'];
        url = body['url'];
      } catch (e) {
        print("Error parsing message body: $e");
      }
    }
    return url;
  }

  void showNotificationInApp(RemoteMessage message) async {
    if (true == message.notification?.title?.isNotEmpty) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          // return object of type Dialog
          return AlertDialog(
            title: Text(message.notification?.title ?? ''),
            content: Text(message.notification?.body ?? ''),
            actions: <Widget>[
              // usually buttons at the bottom of the dialog

              InkWell(
                  onTap: () async {
                    Navigator.pop(context);

                    String? url = _urlFromMessage(message);

                    if (null == url) {
                      return;
                    }

                    Uri uri = Uri.dataFromString(url);

                    if (uri.toString().contains('mailto') ||
                        uri.toString().contains('.ics')) {
                      Uri uri = Uri.parse(url);
                      setState(() {
                        isLoading = true;
                      });
                      await _openLinkExternalBrowser(uri);
                      setState(() {
                        isLoading = false;
                      });
                      loadURLFunction(previousURL);
                    } else {
                      loadURLFunction(url);
                    }
                  },
                  child: const Text("Aceptar")),
            ],
          );
        },
      );
    }
  }

  Future<void> _openLinkExternalBrowser(Uri uri) async {
    log('uri:: $uri');
    try {
      // Check if the URL can be launched externally
      if (uri.scheme == 'mailto') {
        final mailtoUrl = Uri(
          scheme: 'mailto',
          path: uri.path,
          query: uri.query, // Preserve query parameters if any
        );
        if (!await launchUrl(mailtoUrl)) {
          throw Exception('uri::Could not launch mailto $mailtoUrl');
        }
      } else {
        bool canLaunchUrl = await canLaunch(uri.toString());
        if (canLaunchUrl) {
          if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
            throw Exception('uri::Could not launch $uri');
          }
          loadURLFunction(previousURL);
        } else {
          log('uri::Unsupported URL: $uri');

          throw Exception('uri::Unsupported URL: $uri');
        }
      }
    } catch (ex) {
      log("uri::Unable to open URL: $ex");

      showSnackBar("uri::Unable to open URL: $ex");
    }
  }
}
