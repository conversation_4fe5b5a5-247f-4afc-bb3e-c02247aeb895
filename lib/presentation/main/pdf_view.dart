// import 'package:flutter/material.dart';
// import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

// class PDFViewerScreen extends StatelessWidget {
//   final String pdfUrl;

//   const PDFViewerScreen({super.key, required this.pdfUrl});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         backgroundColor: Colors.white,
//         elevation: 0,
//         toolbarHeight: 40,
//         title: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             InkWell(
//               child: const Row(
//                 children: [
//                   Icon(
//                     Icons.arrow_back_ios_new_rounded,
//                     size: 20,
//                     color: Color(0xff003595),
//                   ),
//                   SizedBox(
//                     width: 5,
//                   ),
//                   Text(
//                     "Volver",
//                     style: TextStyle(
//                       fontSize: 16,
//                       fontWeight: FontWeight.bold,
//                       color: Color(0xff003595),
//                     ),
//                   )
//                 ],
//               ),
//               onTap: () {
//                 Navigator.pop(context);
//               },
//             ),
//             Container()
//           ],
//         ),
//       ),
//       body: SfPdfViewer.network(
//         pdfUrl,
//         canShowScrollStatus: true,
//         canShowPaginationDialog: true,
//       ),
//     );
//   }
// }
