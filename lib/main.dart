import 'package:dunalastair/firebase_options.dart';
import 'package:dunalastair/managers/firebase_manager.dart';
import 'package:dunalastair/presentation/initial/splash_page.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  GetIt.instance.registerSingleton<FirebaseManager>(FirebaseManager());
  await GetIt.instance.get<FirebaseManager>().initializeApp();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: '',
      home: const SplashScreen(),
      onGenerateRoute: (_) => SplashScreen.route(),
    );
  }
}
