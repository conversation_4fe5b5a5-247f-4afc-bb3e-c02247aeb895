plugins {
    id 'com.android.application' version '7.3.0' apply false
    id 'com.android.library' version '7.3.0' apply false
    id 'org.jetbrains.kotlin.android' version '2.0.20' apply false
    id 'com.google.gms.google-services' version '4.3.15' apply false
    id 'com.google.firebase.firebase-perf' version '1.4.1' apply false
    id 'com.google.firebase.crashlytics' version '2.8.1' apply false
    id 'com.huawei.agconnect' version '1.5.0.300' apply false
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://developer.huawei.com/repo/' } // HUAWEI Maven repository 
        		maven { url 'https://developer.huawei.com/repo/' } // HUAWEI Maven repository 

    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
