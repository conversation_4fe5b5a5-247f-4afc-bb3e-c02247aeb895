{"project_info": {"project_number": "564728407165", "project_id": "dunalastair-79c5e", "storage_bucket": "dunalastair-79c5e.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:564728407165:android:6b51e97981f94306a33d14", "android_client_info": {"package_name": "cl.gearlabs.colegiosfjh"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyDqzuTuFwbVEwcN-QYLZlufqfdsLSHG1MY"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:564728407165:android:5fc33e7399673716a33d14", "android_client_info": {"package_name": "cl.gearlabs.dunal<PERSON>air"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyDqzuTuFwbVEwcN-QYLZlufqfdsLSHG1MY"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:564728407165:android:5a57ee8db46d8248a33d14", "android_client_info": {"package_name": "com.cognitas.mis"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyDqzuTuFwbVEwcN-QYLZlufqfdsLSHG1MY"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:564728407165:android:f6408f6ea264c382a33d14", "android_client_info": {"package_name": "com.colegio.olinca.app"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyDqzuTuFwbVEwcN-QYLZlufqfdsLSHG1MY"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:564728407165:android:547098d9d995b50ca33d14", "android_client_info": {"package_name": "com.khipu.inside.dunalastair"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyDqzuTuFwbVEwcN-QYLZlufqfdsLSHG1MY"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}], "configuration_version": "1"}